#!/usr/bin/env python3
"""
🎯 Transcription Orchestrator
Główny koordynator pipeline'u transkrypcji HVAC
Email -> M4A -> WAV -> STT -> Gemma Analysis -> Database
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, BackgroundTasks, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import aiohttp
from redis import Redis
import asyncpg
import aiofiles

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/orchestrator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🎯 Modele danych
class TranscriptionJob(BaseModel):
    job_id: str
    source_file: str
    email_source: str
    status: str = "pending"
    created_at: datetime = Field(default_factory=datetime.now)
    
class TranscriptionResult(BaseModel):
    job_id: str
    transcript: str
    confidence: float
    analysis: Optional[Dict] = None
    processing_time: float
    status: str

class PipelineStats(BaseModel):
    total_jobs: int
    completed_jobs: int
    failed_jobs: int
    average_processing_time: float
    last_24h_jobs: int

class TranscriptionOrchestrator:
    """🎯 Główny orchestrator transkrypcji"""
    
    def __init__(self):
        self.app = FastAPI(
            title="🎯 HVAC Transcription Orchestrator",
            description="Koordynator pipeline'u transkrypcji NVIDIA STT + Gemma",
            version="1.0.0"
        )
        
        # 🔧 Konfiguracja CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 🔧 URLs serwisów
        self.service_urls = {
            "nvidia_stt": os.getenv("NVIDIA_STT_URL", "http://nvidia-stt-polish:8889"),
            "audio_converter": os.getenv("AUDIO_CONVERTER_URL", "http://audio-converter:8080"),
            "email_processor": os.getenv("EMAIL_PROCESSOR_URL", "http://email-processor:8080"),
            "gemma_integration": os.getenv("GEMMA_INTEGRATION_URL", "http://gemma-integration:8080"),
            "gobackend": os.getenv("GOBACKEND_URL", "http://host.docker.internal:8080")
        }
        
        # 🔧 Połączenia
        self.redis_client = None
        self.postgres_pool = None
        
        # 📊 Statystyki
        self.job_queue = asyncio.Queue()
        self.active_jobs = {}
        self.websocket_connections = set()
        
        self._setup_routes()
    
    async def initialize(self):
        """🚀 Inicjalizacja orchestratora"""
        logger.info("🚀 Inicjalizacja Transcription Orchestrator...")
        
        try:
            # 📡 Redis
            self.redis_client = Redis(host='redis', port=6379, decode_responses=True)
            self.redis_client.ping()
            logger.info("✅ Połączenie z Redis nawiązane")
            
            # 🐘 PostgreSQL
            postgres_url = os.getenv("POSTGRES_URL")
            if postgres_url:
                self.postgres_pool = await asyncpg.create_pool(postgres_url)
                await self._ensure_database_schema()
                logger.info("✅ Połączenie z PostgreSQL nawiązane")
            
            # 🔍 Test serwisów
            await self._test_service_connections()
            
            # 📁 Katalogi
            self._ensure_directories()
            
            # 🔄 Uruchomienie worker'ów
            asyncio.create_task(self._job_processor_loop())
            asyncio.create_task(self._email_monitoring_loop())
            
            logger.info("✅ Transcription Orchestrator zainicjalizowany!")
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji: {e}")
            raise
    
    def _ensure_directories(self):
        """📁 Sprawdzenie katalogów"""
        directories = ['/app/logs', '/app/config']
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    async def _ensure_database_schema(self):
        """🗄️ Sprawdzenie schematu bazy danych"""
        if not self.postgres_pool:
            return
        
        try:
            async with self.postgres_pool.acquire() as conn:
                # Tabela zadań transkrypcji
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS transcription_jobs (
                        job_id VARCHAR(255) PRIMARY KEY,
                        source_file VARCHAR(500) NOT NULL,
                        email_source VARCHAR(255),
                        status VARCHAR(50) DEFAULT 'pending',
                        transcript TEXT,
                        confidence FLOAT,
                        analysis JSONB,
                        processing_time FLOAT,
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT NOW(),
                        completed_at TIMESTAMP
                    )
                """)
                
                # Tabela komunikacji email
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS email_communications (
                        id SERIAL PRIMARY KEY,
                        message_id VARCHAR(255) UNIQUE,
                        from_email VARCHAR(255),
                        to_email VARCHAR(255),
                        subject TEXT,
                        body TEXT,
                        attachments_count INTEGER DEFAULT 0,
                        account_type VARCHAR(50),
                        processed_at TIMESTAMP DEFAULT NOW()
                    )
                """)
                
                logger.info("✅ Schema bazy danych sprawdzona")
                
        except Exception as e:
            logger.error(f"❌ Błąd sprawdzania schematu: {e}")
    
    async def _test_service_connections(self):
        """🔍 Test połączeń z serwisami"""
        for service_name, url in self.service_urls.items():
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{url}/health", timeout=10) as response:
                        if response.status == 200:
                            logger.info(f"✅ {service_name}: {url}")
                        else:
                            logger.warning(f"⚠️ {service_name}: {url} - status {response.status}")
            except Exception as e:
                logger.warning(f"⚠️ {service_name}: {url} - {e}")
    
    def _setup_routes(self):
        """🛣️ Konfiguracja endpointów"""
        
        @self.app.on_event("startup")
        async def startup_event():
            await self.initialize()
        
        @self.app.get("/health")
        async def health_check():
            """🏥 Status orchestratora"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "services": self.service_urls,
                "active_jobs": len(self.active_jobs),
                "queue_size": self.job_queue.qsize(),
                "websocket_connections": len(self.websocket_connections)
            }
        
        @self.app.post("/transcribe/file")
        async def transcribe_file(
            source_file: str,
            email_source: str = "manual",
            background_tasks: BackgroundTasks = None
        ):
            """🎤 Rozpoczęcie transkrypcji pliku"""
            job_id = f"job_{int(time.time())}_{len(self.active_jobs)}"
            
            job = TranscriptionJob(
                job_id=job_id,
                source_file=source_file,
                email_source=email_source
            )
            
            # Dodanie do kolejki
            await self.job_queue.put(job)
            
            # Zapisanie w bazie
            if self.postgres_pool:
                await self._save_job_to_database(job)
            
            return {"job_id": job_id, "status": "queued"}
        
        @self.app.get("/transcribe/status/{job_id}")
        async def get_job_status(job_id: str):
            """📊 Status zadania"""
            # Sprawdzenie aktywnych zadań
            if job_id in self.active_jobs:
                return self.active_jobs[job_id]
            
            # Sprawdzenie w bazie danych
            if self.postgres_pool:
                result = await self._get_job_from_database(job_id)
                if result:
                    return result
            
            raise HTTPException(status_code=404, detail="Zadanie nie znalezione")
        
        @self.app.get("/stats", response_model=PipelineStats)
        async def get_pipeline_stats():
            """📊 Statystyki pipeline'u"""
            return await self._get_pipeline_statistics()
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """🔌 WebSocket dla real-time updates"""
            await websocket.accept()
            self.websocket_connections.add(websocket)
            
            try:
                while True:
                    await websocket.receive_text()
            except:
                pass
            finally:
                self.websocket_connections.discard(websocket)
        
        @self.app.post("/trigger/email-check")
        async def trigger_email_check():
            """📧 Ręczne sprawdzenie emaili"""
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.service_urls['email_processor']}/process/manual"
                    ) as response:
                        result = await response.json()
                        return {"status": "triggered", "result": result}
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Błąd: {str(e)}")
    
    async def _job_processor_loop(self):
        """🔄 Główna pętla przetwarzania zadań"""
        logger.info("🔄 Uruchomienie job processor loop...")
        
        while True:
            try:
                # Pobranie zadania z kolejki
                job = await self.job_queue.get()
                
                # Dodanie do aktywnych zadań
                self.active_jobs[job.job_id] = job.dict()
                
                # Przetwarzanie zadania
                await self._process_transcription_job(job)
                
                # Usunięcie z aktywnych zadań
                if job.job_id in self.active_jobs:
                    del self.active_jobs[job.job_id]
                
            except Exception as e:
                logger.error(f"❌ Błąd w job processor: {e}")
                await asyncio.sleep(5)
    
    async def _email_monitoring_loop(self):
        """📧 Pętla monitorowania emaili"""
        logger.info("📧 Uruchomienie email monitoring loop...")
        
        while True:
            try:
                # Sprawdzenie nowych emaili co 5 minut
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.service_urls['email_processor']}/process/manual"
                    ) as response:
                        if response.status == 200:
                            result = await response.json()
                            logger.info(f"📧 Email check result: {result}")
                
                await asyncio.sleep(300)  # 5 minut
                
            except Exception as e:
                logger.error(f"❌ Błąd w email monitoring: {e}")
                await asyncio.sleep(60)
    
    async def _process_transcription_job(self, job: TranscriptionJob):
        """🎤 Przetwarzanie zadania transkrypcji"""
        start_time = time.time()
        
        try:
            logger.info(f"🎤 Rozpoczęcie przetwarzania: {job.job_id}")
            
            # Aktualizacja statusu
            await self._update_job_status(job.job_id, "processing")
            
            # Krok 1: Konwersja audio (jeśli potrzebna)
            converted_file = await self._convert_audio_if_needed(job.source_file)
            
            # Krok 2: Transkrypcja NVIDIA STT
            transcript_result = await self._perform_transcription(converted_file or job.source_file)
            
            if not transcript_result or "transcript" not in transcript_result:
                raise Exception("Błąd transkrypcji")
            
            # Krok 3: Analiza z Gemma
            analysis_result = await self._perform_gemma_analysis(
                transcript_result["transcript"], 
                job.source_file
            )
            
            # Krok 4: Zapisanie wyników
            final_result = TranscriptionResult(
                job_id=job.job_id,
                transcript=transcript_result["transcript"],
                confidence=transcript_result.get("confidence", 0.8),
                analysis=analysis_result,
                processing_time=time.time() - start_time,
                status="completed"
            )
            
            await self._save_transcription_result(final_result)
            await self._update_job_status(job.job_id, "completed")
            
            # Powiadomienie WebSocket
            await self._notify_websocket_clients(final_result.dict())
            
            logger.info(f"✅ Zadanie zakończone: {job.job_id} ({final_result.processing_time:.2f}s)")
            
        except Exception as e:
            logger.error(f"❌ Błąd przetwarzania {job.job_id}: {e}")
            await self._update_job_status(job.job_id, "failed", str(e))
    
    async def _convert_audio_if_needed(self, file_path: str) -> Optional[str]:
        """🔄 Konwersja audio jeśli potrzebna"""
        if not file_path.lower().endswith('.wav'):
            try:
                async with aiohttp.ClientSession() as session:
                    with open(file_path, 'rb') as f:
                        data = aiohttp.FormData()
                        data.add_field('audio_file', f, filename=os.path.basename(file_path))
                        data.add_field('output_format', 'wav')
                        data.add_field('sample_rate', '16000')
                        
                        async with session.post(
                            f"{self.service_urls['audio_converter']}/convert",
                            data=data
                        ) as response:
                            if response.status == 200:
                                result = await response.json()
                                return result.get("output_file_path")
                            
            except Exception as e:
                logger.error(f"❌ Błąd konwersji audio: {e}")
        
        return None
    
    async def _perform_transcription(self, file_path: str) -> Dict:
        """🎤 Wykonanie transkrypcji"""
        try:
            async with aiohttp.ClientSession() as session:
                with open(file_path, 'rb') as f:
                    data = aiohttp.FormData()
                    data.add_field('audio_file', f, filename=os.path.basename(file_path))
                    data.add_field('language', 'pl')
                    data.add_field('model_type', 'fastconformer')
                    data.add_field('hvac_context', 'true')
                    
                    async with session.post(
                        f"{self.service_urls['nvidia_stt']}/transcribe",
                        data=data
                    ) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            raise Exception(f"STT API error: {response.status}")
                            
        except Exception as e:
            logger.error(f"❌ Błąd transkrypcji: {e}")
            raise
    
    async def _perform_gemma_analysis(self, transcript: str, source_file: str) -> Dict:
        """🧠 Analiza z Gemma"""
        try:
            payload = {
                "transcript": transcript,
                "source_file": os.path.basename(source_file),
                "hvac_context": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.service_urls['gemma_integration']}/analyze",
                    json=payload
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"⚠️ Gemma analysis failed: {response.status}")
                        return {}
                        
        except Exception as e:
            logger.error(f"❌ Błąd analizy Gemma: {e}")
            return {}
    
    async def _save_transcription_result(self, result: TranscriptionResult):
        """💾 Zapisanie wyniku transkrypcji"""
        if not self.postgres_pool:
            return
        
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute("""
                    UPDATE transcription_jobs 
                    SET transcript = $1, confidence = $2, analysis = $3, 
                        processing_time = $4, status = $5, completed_at = NOW()
                    WHERE job_id = $6
                """,
                result.transcript,
                result.confidence,
                json.dumps(result.analysis) if result.analysis else None,
                result.processing_time,
                result.status,
                result.job_id
                )
                
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania wyniku: {e}")
    
    async def _save_job_to_database(self, job: TranscriptionJob):
        """💾 Zapisanie zadania do bazy"""
        if not self.postgres_pool:
            return
        
        try:
            async with self.postgres_pool.acquire() as conn:
                await conn.execute("""
                    INSERT INTO transcription_jobs (job_id, source_file, email_source, status, created_at)
                    VALUES ($1, $2, $3, $4, $5)
                """,
                job.job_id, job.source_file, job.email_source, job.status, job.created_at
                )
                
        except Exception as e:
            logger.error(f"❌ Błąd zapisywania zadania: {e}")
    
    async def _get_job_from_database(self, job_id: str) -> Optional[Dict]:
        """📊 Pobranie zadania z bazy"""
        if not self.postgres_pool:
            return None
        
        try:
            async with self.postgres_pool.acquire() as conn:
                row = await conn.fetchrow(
                    "SELECT * FROM transcription_jobs WHERE job_id = $1", job_id
                )
                
                if row:
                    return dict(row)
                    
        except Exception as e:
            logger.error(f"❌ Błąd pobierania zadania: {e}")
        
        return None
    
    async def _update_job_status(self, job_id: str, status: str, error_message: str = None):
        """📊 Aktualizacja statusu zadania"""
        # Aktualizacja w pamięci
        if job_id in self.active_jobs:
            self.active_jobs[job_id]["status"] = status
            if error_message:
                self.active_jobs[job_id]["error_message"] = error_message
        
        # Aktualizacja w bazie
        if self.postgres_pool:
            try:
                async with self.postgres_pool.acquire() as conn:
                    await conn.execute("""
                        UPDATE transcription_jobs 
                        SET status = $1, error_message = $2
                        WHERE job_id = $3
                    """, status, error_message, job_id)
            except Exception as e:
                logger.error(f"❌ Błąd aktualizacji statusu: {e}")
    
    async def _get_pipeline_statistics(self) -> PipelineStats:
        """📊 Statystyki pipeline'u"""
        if not self.postgres_pool:
            return PipelineStats(
                total_jobs=0, completed_jobs=0, failed_jobs=0,
                average_processing_time=0.0, last_24h_jobs=0
            )
        
        try:
            async with self.postgres_pool.acquire() as conn:
                # Podstawowe statystyki
                stats = await conn.fetchrow("""
                    SELECT 
                        COUNT(*) as total_jobs,
                        COUNT(*) FILTER (WHERE status = 'completed') as completed_jobs,
                        COUNT(*) FILTER (WHERE status = 'failed') as failed_jobs,
                        AVG(processing_time) FILTER (WHERE processing_time IS NOT NULL) as avg_time,
                        COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as last_24h
                    FROM transcription_jobs
                """)
                
                return PipelineStats(
                    total_jobs=stats['total_jobs'] or 0,
                    completed_jobs=stats['completed_jobs'] or 0,
                    failed_jobs=stats['failed_jobs'] or 0,
                    average_processing_time=float(stats['avg_time'] or 0.0),
                    last_24h_jobs=stats['last_24h'] or 0
                )
                
        except Exception as e:
            logger.error(f"❌ Błąd pobierania statystyk: {e}")
            return PipelineStats(
                total_jobs=0, completed_jobs=0, failed_jobs=0,
                average_processing_time=0.0, last_24h_jobs=0
            )
    
    async def _notify_websocket_clients(self, data: Dict):
        """🔌 Powiadomienie klientów WebSocket"""
        if not self.websocket_connections:
            return
        
        message = json.dumps(data)
        disconnected = set()
        
        for websocket in self.websocket_connections:
            try:
                await websocket.send_text(message)
            except:
                disconnected.add(websocket)
        
        # Usunięcie rozłączonych połączeń
        self.websocket_connections -= disconnected

# 🚀 Uruchomienie orchestratora
if __name__ == "__main__":
    orchestrator = TranscriptionOrchestrator()
    
    uvicorn.run(
        orchestrator.app,
        host="0.0.0.0",
        port=8080,
        log_level="info"
    )
