#!/usr/bin/env python3
"""
🎤 NVIDIA STT Server dla języka polskiego
Serwer transkrypcji wykorzystujący NVIDIA NeMo FastConformer Hybrid Large PC
Zoptymalizowany dla polskich rozmów HVAC
"""

import os
import asyncio
import logging
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

import torch
import nemo.collections.asr as nemo_asr
from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import librosa
import soundfile as sf
import numpy as np
from redis import Redis
import aiofiles

# 🔧 Konfiguracja logowania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/workspace/logs/stt_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🎯 Modele danych
class TranscriptionRequest(BaseModel):
    audio_file_path: str
    language: str = "pl"
    model_type: str = "fastconformer"
    hvac_context: bool = True
    speaker_diarization: bool = False
    
class TranscriptionResponse(BaseModel):
    transcript: str
    confidence: float
    processing_time: float
    language_detected: str
    word_timestamps: Optional[List[Dict]] = None
    speaker_segments: Optional[List[Dict]] = None
    hvac_keywords: Optional[List[str]] = None

class HVACSTTServer:
    """🎤 Główny serwer NVIDIA STT dla HVAC"""
    
    def __init__(self):
        self.app = FastAPI(
            title="🎤 HVAC NVIDIA STT Server",
            description="Serwer transkrypcji NVIDIA NeMo dla języka polskiego - HVAC CRM",
            version="1.0.0"
        )
        
        # 🔧 Konfiguracja CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 🧠 Inicjalizacja modeli
        self.models = {}
        self.redis_client = None
        self.hvac_keywords = [
            "klimatyzacja", "klimatyzator", "pompa ciepła", "wentylacja",
            "chłodzenie", "ogrzewanie", "serwis", "naprawa", "instalacja",
            "LG", "Daikin", "Samsung", "Mitsubishi", "Fujitsu",
            "split", "multi split", "VRF", "VRV", "inverter",
            "filtr", "czynnik chłodniczy", "R32", "R410A",
            "temperatura", "wilgotność", "termostat", "pilot"
        ]
        
        self._setup_routes()
        
    async def initialize(self):
        """🚀 Inicjalizacja serwera i modeli"""
        logger.info("🚀 Inicjalizacja HVAC STT Server...")
        
        try:
            # 📡 Połączenie z Redis
            self.redis_client = Redis(host='redis', port=6379, decode_responses=True)
            await self._test_redis_connection()
            
            # 🧠 Ładowanie modeli NVIDIA NeMo
            await self._load_stt_models()
            
            logger.info("✅ HVAC STT Server zainicjalizowany pomyślnie!")
            
        except Exception as e:
            logger.error(f"❌ Błąd inicjalizacji: {e}")
            raise
    
    async def _test_redis_connection(self):
        """🔍 Test połączenia z Redis"""
        try:
            self.redis_client.ping()
            logger.info("✅ Połączenie z Redis nawiązane")
        except Exception as e:
            logger.error(f"❌ Błąd połączenia z Redis: {e}")
            raise
    
    async def _load_stt_models(self):
        """🧠 Ładowanie modeli STT"""
        logger.info("🧠 Ładowanie modeli NVIDIA NeMo...")
        
        try:
            # 🎯 Model główny - Multilingual FastConformer Hybrid Large PC
            logger.info("📥 Ładowanie modelu FastConformer Hybrid Large PC...")
            self.models['fastconformer'] = nemo_asr.models.EncDecCTCModel.from_pretrained(
                "stt_multilingual_fastconformer_hybrid_large_pc"
            )
            
            # 🎯 Model backup - QuartzNet Polish
            logger.info("📥 Ładowanie modelu QuartzNet Polish...")
            self.models['quartznet'] = nemo_asr.models.EncDecCTCModel.from_pretrained(
                "stt_pl_quartznet15x5"
            )
            
            # 🔧 Konfiguracja dla języka polskiego
            for model_name, model in self.models.items():
                if hasattr(model, 'change_vocabulary'):
                    # Dodanie słownictwa HVAC
                    hvac_vocab = self.hvac_keywords
                    logger.info(f"🔧 Konfiguracja słownictwa HVAC dla {model_name}")
            
            logger.info("✅ Wszystkie modele STT załadowane pomyślnie!")
            
        except Exception as e:
            logger.error(f"❌ Błąd ładowania modeli: {e}")
            raise
    
    def _setup_routes(self):
        """🛣️ Konfiguracja endpointów API"""
        
        @self.app.on_event("startup")
        async def startup_event():
            await self.initialize()
        
        @self.app.get("/health")
        async def health_check():
            """🏥 Sprawdzenie stanu serwera"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "models_loaded": list(self.models.keys()),
                "gpu_available": torch.cuda.is_available(),
                "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
            }
        
        @self.app.post("/transcribe", response_model=TranscriptionResponse)
        async def transcribe_audio(
            background_tasks: BackgroundTasks,
            audio_file: UploadFile = File(...),
            language: str = "pl",
            model_type: str = "fastconformer",
            hvac_context: bool = True
        ):
            """🎤 Główny endpoint transkrypcji"""
            return await self._transcribe_audio_file(
                audio_file, language, model_type, hvac_context, background_tasks
            )
        
        @self.app.post("/transcribe/batch")
        async def transcribe_batch(
            background_tasks: BackgroundTasks,
            files: List[UploadFile] = File(...),
            language: str = "pl",
            model_type: str = "fastconformer"
        ):
            """📦 Transkrypcja wsadowa"""
            results = []
            for file in files:
                result = await self._transcribe_audio_file(
                    file, language, model_type, True, background_tasks
                )
                results.append(result)
            return {"results": results}
        
        @self.app.get("/models")
        async def get_available_models():
            """📋 Lista dostępnych modeli"""
            return {
                "available_models": list(self.models.keys()),
                "default_model": "fastconformer",
                "supported_languages": ["pl", "en", "de", "fr", "es"],
                "hvac_keywords_count": len(self.hvac_keywords)
            }
    
    async def _transcribe_audio_file(
        self, 
        audio_file: UploadFile, 
        language: str, 
        model_type: str, 
        hvac_context: bool,
        background_tasks: BackgroundTasks
    ) -> TranscriptionResponse:
        """🎤 Transkrypcja pojedynczego pliku audio"""
        
        start_time = time.time()
        
        try:
            # 💾 Zapisanie pliku tymczasowego
            temp_path = f"/workspace/audio_input/{audio_file.filename}"
            async with aiofiles.open(temp_path, 'wb') as f:
                content = await audio_file.read()
                await f.write(content)
            
            # 🔄 Preprocessing audio
            audio_data, sample_rate = await self._preprocess_audio(temp_path)
            
            # 🧠 Wybór modelu
            model = self.models.get(model_type, self.models['fastconformer'])
            
            # 🎯 Transkrypcja
            transcript = model.transcribe([temp_path])[0]
            
            # 🔍 Analiza HVAC keywords
            hvac_keywords_found = []
            if hvac_context:
                hvac_keywords_found = self._extract_hvac_keywords(transcript)
            
            # 📊 Obliczenie confidence (uproszczone)
            confidence = self._calculate_confidence(transcript, audio_data)
            
            processing_time = time.time() - start_time
            
            # 🗑️ Czyszczenie pliku tymczasowego w tle
            background_tasks.add_task(self._cleanup_temp_file, temp_path)
            
            # 💾 Zapisanie do cache
            await self._cache_transcription_result(audio_file.filename, transcript, confidence)
            
            logger.info(f"✅ Transkrypcja zakończona: {audio_file.filename} ({processing_time:.2f}s)")
            
            return TranscriptionResponse(
                transcript=transcript,
                confidence=confidence,
                processing_time=processing_time,
                language_detected=language,
                hvac_keywords=hvac_keywords_found if hvac_context else None
            )
            
        except Exception as e:
            logger.error(f"❌ Błąd transkrypcji {audio_file.filename}: {e}")
            raise HTTPException(status_code=500, detail=f"Błąd transkrypcji: {str(e)}")
    
    async def _preprocess_audio(self, file_path: str) -> tuple:
        """🔄 Preprocessing pliku audio"""
        try:
            # Ładowanie audio z librosa
            audio_data, sample_rate = librosa.load(file_path, sr=16000, mono=True)
            
            # Normalizacja
            audio_data = librosa.util.normalize(audio_data)
            
            # Usunięcie ciszy na początku i końcu
            audio_data, _ = librosa.effects.trim(audio_data, top_db=20)
            
            return audio_data, sample_rate
            
        except Exception as e:
            logger.error(f"❌ Błąd preprocessing audio: {e}")
            raise
    
    def _extract_hvac_keywords(self, transcript: str) -> List[str]:
        """🔍 Wyodrębnienie słów kluczowych HVAC"""
        found_keywords = []
        transcript_lower = transcript.lower()
        
        for keyword in self.hvac_keywords:
            if keyword.lower() in transcript_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _calculate_confidence(self, transcript: str, audio_data: np.ndarray) -> float:
        """📊 Obliczenie confidence score (uproszczone)"""
        # Uproszczona kalkulacja na podstawie długości transkrypcji i audio
        transcript_length = len(transcript.split())
        audio_duration = len(audio_data) / 16000  # 16kHz sample rate
        
        if audio_duration > 0:
            words_per_second = transcript_length / audio_duration
            # Normalizacja do zakresu 0.5-0.95
            confidence = min(0.95, max(0.5, words_per_second / 3.0))
        else:
            confidence = 0.5
        
        return round(confidence, 3)
    
    async def _cache_transcription_result(self, filename: str, transcript: str, confidence: float):
        """💾 Zapisanie wyniku do cache Redis"""
        try:
            cache_key = f"transcription:{filename}"
            cache_data = {
                "transcript": transcript,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat()
            }
            
            self.redis_client.setex(
                cache_key, 
                3600,  # 1 godzina TTL
                json.dumps(cache_data)
            )
            
        except Exception as e:
            logger.warning(f"⚠️ Nie udało się zapisać do cache: {e}")
    
    async def _cleanup_temp_file(self, file_path: str):
        """🗑️ Usunięcie pliku tymczasowego"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"🗑️ Usunięto plik tymczasowy: {file_path}")
        except Exception as e:
            logger.warning(f"⚠️ Nie udało się usunąć pliku tymczasowego: {e}")

# 🚀 Uruchomienie serwera
if __name__ == "__main__":
    server = HVACSTTServer()
    
    uvicorn.run(
        server.app,
        host="0.0.0.0",
        port=8889,
        log_level="info",
        access_log=True
    )
